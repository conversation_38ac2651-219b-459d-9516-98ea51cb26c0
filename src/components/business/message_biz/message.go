package message_biz

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// MessageVO 消息视图对象
type MessageVO struct {
	ID        uint                    `json:"id"`
	Type      constmap.MessageType    `json:"type"`
	SubType   constmap.MessageSubType `json:"sub_type"`
	Title     string                  `json:"title"`
	Content   string                  `json:"content"`
	IsRead    int                     `json:"is_read"`
	ReadTime  int64                   `json:"read_time"`  // unix时间戳，0表示未读
	CreatedAt int64                   `json:"created_at"` // unix时间戳

	// 业务字段
	WishCommentId uint `json:"wish_comment_id,omitempty"`
	WishMemberId  uint `json:"wish_member_id,omitempty"`
}

// MessageTypeSummary 消息类型汇总
type MessageTypeSummary struct {
	Total       int64        `json:"total"`
	UnreadCount int64        `json:"unread_count"`
	Messages    []*MessageVO `json:"messages"`
}

// MessageBadge 消息徽章
type MessageBadge struct {
	SystemUnread  int64 `json:"system_unread"`
	TeamUnread    int64 `json:"team_unread"`
	CommentUnread int64 `json:"comment_unread"`
	TotalUnread   int64 `json:"total_unread"`
}

// GetMessageTypeSummary 获取指定类型消息汇总（带缓存）
func GetMessageTypeSummary(db *gorm.DB, userId uint, msgType constmap.MessageType) (*MessageTypeSummary, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType)
	var summary = &utils.Marshaller[MessageTypeSummary]{new(MessageTypeSummary)}

	if my_cache.Get(cacheKey, &summary) {
		return summary.Data, nil
	}

	limit := 3
	// 缓存未命中，查询数据库
	// 1. 获取全员消息（scope=1）前3条
	globalMessages, err := GetGlobalMessages(db, userId, msgType, limit)
	if err != nil {
		return nil, err
	}

	// 2. 获取个人消息（scope=2）前3条
	personalMessages, err := GetPersonalMessages(db, userId, msgType, limit)
	if err != nil {
		return nil, err
	}

	// 3. 合并消息并取前3条
	allMessages := MergeMessages(globalMessages, personalMessages, limit)

	// 4. 统计总数和未读数
	total, unreadCount, err := GetMessageCounts(db, userId, msgType)
	if err != nil {
		return nil, err
	}

	summary.Data = &MessageTypeSummary{
		Total:       total,
		UnreadCount: unreadCount,
		Messages:    allMessages,
	}

	_ = my_cache.Set(cacheKey, summary, constmap.TimeDur10m)

	return summary.Data, nil
}

// GetGlobalMessages 获取全员消息（scope=1）优化版：精准查询
func GetGlobalMessages(db *gorm.DB, userId uint, msgType constmap.MessageType, limit int) ([]*MessageVO, error) {
	// 直接查询指定类型的全员消息，限制条数为1条（压缩查询）
	var templates []*models.MessageTpl
	err := db.Where("type = ? AND scope = 1", msgType).
		Order("created_at DESC").
		Limit(limit). // 每种类型只查1条
		Find(&templates).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	if len(templates) == 0 {
		return []*MessageVO{}, nil
	}

	// 获取用户对这些消息的状态
	statusMap, err := GetUserMessageStatusMap(db, userId, extractTemplateIds(templates))
	if err != nil {
		return nil, err
	}

	// 转换为VO
	var messages []*MessageVO
	for _, tpl := range templates {
		vo := &MessageVO{
			ID:            tpl.ID,
			Type:          tpl.Type,
			SubType:       tpl.SubType,
			Title:         tpl.Title,
			Content:       tpl.Content,
			IsRead:        constmap.Disable, // 默认未读
			CreatedAt:     tpl.CreatedAt.Unix(),
			WishCommentId: tpl.WishCommentId,
			WishMemberId:  tpl.WishMemberId,
		}

		// 设置用户状态
		if status, exists := statusMap[tpl.ID]; exists {
			vo.IsRead = status.IsRead
			if status.ReadTime != nil {
				vo.ReadTime = status.ReadTime.Unix()
			}
		}

		messages = append(messages, vo)
	}

	return messages, nil
}

// GetPersonalMessages 获取个人消息（scope=2）
func GetPersonalMessages(db *gorm.DB, userId uint, msgType constmap.MessageType, limit int) ([]*MessageVO, error) {
	// 使用ORM格式查询，可读性更好
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	err := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, message.is_read, message.read_time").
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId).
		Order("message_tpl.created_at DESC").
		Limit(limit).
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	// 转换为VO
	var messages []*MessageVO
	for _, result := range results {
		vo := &MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}

		// 转换ReadTime为unix时间戳
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}

		messages = append(messages, vo)
	}

	return messages, nil
}

// GetUserMessageStatusMap 获取用户消息状态映射（带缓存）
func GetUserMessageStatusMap(db *gorm.DB, userId uint, templateIds []uint) (map[uint]*models.Message, error) {
	if len(templateIds) == 0 {
		return make(map[uint]*models.Message), nil
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf(constmap.RKUserMsgStatus, userId)
	var cachedStatusMap map[uint]*models.Message

	if my_cache.Get(cacheKey, &cachedStatusMap) {
		// 过滤出需要的消息状态
		filteredMap := make(map[uint]*models.Message)
		for _, id := range templateIds {
			if status, exists := cachedStatusMap[id]; exists {
				filteredMap[id] = status
			}
		}
		return filteredMap, nil
	}

	// 缓存未命中，查询数据库
	var messages []*models.Message
	err := db.Where("user_id = ? AND message_tpl_id IN ?", userId, templateIds).
		Find(&messages).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 转换为map
	statusMap := make(map[uint]*models.Message)
	for _, msg := range messages {
		statusMap[msg.MessageTplId] = msg
	}

	// 缓存10分钟
	_ = my_cache.Set(cacheKey, statusMap, 10*time.Minute)

	return statusMap, nil
}

// MergeMessages 合并消息列表
func MergeMessages(globalMessages, personalMessages []*MessageVO, limit int) []*MessageVO {
	// 合并两个列表
	allMessages := append(globalMessages, personalMessages...)

	// 按创建时间倒序排序（unix时间戳，数值越大越新）
	for i := 0; i < len(allMessages)-1; i++ {
		for j := i + 1; j < len(allMessages); j++ {
			if allMessages[i].CreatedAt < allMessages[j].CreatedAt {
				allMessages[i], allMessages[j] = allMessages[j], allMessages[i]
			}
		}
	}

	// 取前limit条
	if len(allMessages) > limit {
		allMessages = allMessages[:limit]
	}

	return allMessages
}

// MessageTypeStats 消息类型统计数据
type MessageTypeStats struct {
	Total       int64 `json:"total"`
	UnreadCount int64 `json:"unread_count"`
}

// GetMessageCounts 获取消息统计数量（优化版：分离查询 + 缓存）
func GetMessageCounts(db *gorm.DB, userId uint, msgType constmap.MessageType) (total, unreadCount int64, err error) {
	// 尝试从缓存获取统计数据
	cacheKey := fmt.Sprintf(constmap.RKMsgTypeStats, userId, msgType)
	var stats *MessageTypeStats

	if my_cache.Get(cacheKey, &stats) {
		return stats.Total, stats.UnreadCount, nil
	}

	// 缓存未命中，分离查询避免复杂JOIN
	// 1. 统计全员消息
	var globalTotal int64
	err = db.Model(&models.MessageTpl{}).
		Where("type = ? AND scope = 1", msgType).
		Count(&globalTotal).Error
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	// 2. 统计全员消息已读数
	var globalRead int64
	err = db.Model(&models.Message{}).
		Joins("INNER JOIN message_tpl ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 1 AND message.user_id = ? AND message.is_read = 1", msgType, userId).
		Count(&globalRead).Error
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	// 3. 统计个人消息（总数和未读数一起查）
	var personalTotal, personalUnread int64
	err = db.Model(&models.Message{}).
		Select("COUNT(*) as total, COUNT(CASE WHEN is_read = 2 THEN 1 END) as unread").
		Joins("INNER JOIN message_tpl ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId).
		Row().Scan(&personalTotal, &personalUnread)
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	globalUnread := globalTotal - globalRead
	total = globalTotal + personalTotal
	unreadCount = globalUnread + personalUnread

	// 缓存统计结果5分钟
	stats = &MessageTypeStats{
		Total:       total,
		UnreadCount: unreadCount,
	}
	_ = my_cache.Set(cacheKey, stats, 5*time.Minute)

	return total, unreadCount, nil
}

// GetMessageBadge 获取消息徽章（未读数统计）优化版：批量查询
func GetMessageBadge(db *gorm.DB, userId uint) (*MessageBadge, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf(constmap.RKMsgBadge, userId)
	var badge = &utils.Marshaller[MessageBadge]{new(MessageBadge)}

	if my_cache.Get(cacheKey, &badge) {
		return badge.Data, nil
	}

	// 缓存未命中，使用ORM格式批量查询所有类型的统计数据
	var results []struct {
		Type           int   `gorm:"column:type"`
		GlobalTotal    int64 `gorm:"column:global_total"`
		GlobalRead     int64 `gorm:"column:global_read"`
		PersonalTotal  int64 `gorm:"column:personal_total"`
		PersonalUnread int64 `gorm:"column:personal_unread"`
	}

	err := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.type,
			COUNT(CASE WHEN message_tpl.scope = 1 THEN 1 END) as global_total,
			COUNT(CASE WHEN message_tpl.scope = 1 AND message.is_read = 1 THEN 1 END) as global_read,
			COUNT(CASE WHEN message_tpl.scope = 2 THEN 1 END) as personal_total,
			COUNT(CASE WHEN message_tpl.scope = 2 AND message.is_read = 2 THEN 1 END) as personal_unread
		`).
		Joins("LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?", userId).
		Where("message_tpl.type IN ?", []constmap.MessageType{
			constmap.MessageTypeSystem,
			constmap.MessageTypeTeam,
			constmap.MessageTypeComment,
		}).
		Group("message_tpl.type").
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	badge.Data = &MessageBadge{}
	badgeData := badge.Data

	for _, result := range results {
		unreadCount := (result.GlobalTotal - result.GlobalRead) + result.PersonalUnread

		switch constmap.MessageType(result.Type) {
		case constmap.MessageTypeSystem:
			badgeData.SystemUnread = unreadCount
		case constmap.MessageTypeTeam:
			badgeData.TeamUnread = unreadCount
		case constmap.MessageTypeComment:
			badgeData.CommentUnread = unreadCount
		}
	}

	badgeData.TotalUnread = badgeData.SystemUnread + badgeData.TeamUnread + badgeData.CommentUnread

	_ = my_cache.Set(cacheKey, badge, constmap.TimeDur10m)

	return badge.Data, nil
}

// BatchSetMessageRead 批量设置消息已读（仅普通文本消息）
func BatchSetMessageRead(db *gorm.DB, userId uint, messageIds []uint) error {
	if len(messageIds) == 0 {
		return nil
	}

	// 查询符合条件的消息模板（仅普通文本消息）
	var validTemplates []*models.MessageTpl
	err := db.Where("id IN ? AND sub_type = ?", messageIds, constmap.MessageSubTypeText).
		Find(&validTemplates).Error
	if err != nil {
		return utils.NewError(err)
	}

	if len(validTemplates) == 0 {
		return nil
	}

	validIds := make([]uint, len(validTemplates))
	for i, tpl := range validTemplates {
		validIds[i] = tpl.ID
	}

	// 批量创建或更新消息状态
	now := time.Now()
	var messages []*models.Message

	for _, templateId := range validIds {
		messages = append(messages, &models.Message{
			MessageTplId: templateId,
			UserId:       userId,
			IsRead:       constmap.Enable,
			ReadTime:     &now,
		})
	}

	// 使用事务批量插入或更新
	err = db.Transaction(func(tx *gorm.DB) error {
		for _, msg := range messages {
			err := tx.Where(models.Message{MessageTplId: msg.MessageTplId, UserId: msg.UserId}).
				Assign(models.Message{IsRead: constmap.Enable, ReadTime: &now}).
				FirstOrCreate(msg).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存
	ClearUserMessageCache(userId)

	return nil
}

// 批量获取指定类型消息汇总（优化版：按类型分别缓存）
func GetMessageTypeSummaries(db *gorm.DB, userId uint, messageTypes []constmap.MessageType, messageLimit int) (map[constmap.MessageType]*MessageTypeSummary, error) {
	summaryMap := make(map[constmap.MessageType]*MessageTypeSummary)
	var uncachedTypes []constmap.MessageType

	// 1. 尝试从缓存获取每种类型的汇总
	for _, msgType := range messageTypes {
		cacheKey := fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType)
		var summary *MessageTypeSummary

		if my_cache.Get(cacheKey, &summary) {
			summaryMap[msgType] = summary
		} else {
			uncachedTypes = append(uncachedTypes, msgType)
		}
	}

	// 2. 查询未缓存的类型
	if len(uncachedTypes) > 0 {
		// 批量查询统计数据
		statsMap, err := batchGetMessageStats(db, userId, uncachedTypes)
		if err != nil {
			return nil, err
		}

		// 批量查询最新消息
		messagesMap, err := batchGetLatestMessages(db, userId, uncachedTypes, messageLimit)
		if err != nil {
			return nil, err
		}

		// 组装结果并缓存
		for _, msgType := range uncachedTypes {
			stats := statsMap[msgType]
			messages := messagesMap[msgType]

			summary := &MessageTypeSummary{
				Total:       stats.Total,
				UnreadCount: stats.UnreadCount,
				Messages:    messages,
			}

			summaryMap[msgType] = summary

			cacheKey := fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType)
			_ = my_cache.Set(cacheKey, summary, constmap.TimeDur10m)
		}
	}

	return summaryMap, nil
}

// batchGetMessageStats 批量获取消息统计数据
func batchGetMessageStats(db *gorm.DB, userId uint, messageTypes []constmap.MessageType) (map[constmap.MessageType]*MessageTypeStats, error) {
	var results []struct {
		Type           int   `gorm:"column:type"`
		GlobalTotal    int64 `gorm:"column:global_total"`
		GlobalRead     int64 `gorm:"column:global_read"`
		PersonalTotal  int64 `gorm:"column:personal_total"`
		PersonalUnread int64 `gorm:"column:personal_unread"`
	}

	err := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.type,
			COUNT(CASE WHEN message_tpl.scope = 1 THEN 1 END) as global_total,
			COUNT(CASE WHEN message_tpl.scope = 1 AND message.is_read = 1 THEN 1 END) as global_read,
			COUNT(CASE WHEN message_tpl.scope = 2 THEN 1 END) as personal_total,
			COUNT(CASE WHEN message_tpl.scope = 2 AND message.is_read = 2 THEN 1 END) as personal_unread
		`).
		Joins("LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?", userId).
		Where("message_tpl.type IN ?", messageTypes).
		Group("message_tpl.type").
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	statsMap := make(map[constmap.MessageType]*MessageTypeStats)
	for _, result := range results {
		msgType := constmap.MessageType(result.Type)
		unreadCount := (result.GlobalTotal - result.GlobalRead) + result.PersonalUnread
		total := result.GlobalTotal + result.PersonalTotal

		statsMap[msgType] = &MessageTypeStats{
			Total:       total,
			UnreadCount: unreadCount,
		}
	}

	// 确保所有类型都有数据（即使为空）
	for _, msgType := range messageTypes {
		if _, exists := statsMap[msgType]; !exists {
			statsMap[msgType] = &MessageTypeStats{
				Total:       0,
				UnreadCount: 0,
			}
		}
	}

	return statsMap, nil
}

// batchGetLatestMessages 批量获取最新消息
func batchGetLatestMessages(db *gorm.DB, userId uint, messageTypes []constmap.MessageType, limit int) (map[constmap.MessageType][]*MessageVO, error) {
	messagesMap := make(map[constmap.MessageType][]*MessageVO)

	for _, msgType := range messageTypes {
		// 获取全员消息
		globalMessages, err := GetGlobalMessages(db, userId, msgType, limit)
		if err != nil {
			return nil, err
		}

		// 获取个人消息
		personalMessages, err := GetPersonalMessages(db, userId, msgType, limit)
		if err != nil {
			return nil, err
		}

		// 合并并取前limit条
		allMessages := MergeMessages(globalMessages, personalMessages, limit)
		messagesMap[msgType] = allMessages
	}

	return messagesMap, nil
}

// GetMessageListWithPagination 获取消息列表（支持分页和筛选）
func GetMessageListWithPagination(db *gorm.DB, userId uint, msgType constmap.MessageType, isRead int, page, pageSize int) ([]*MessageVO, int64, error) {
	// 1. 获取全员消息
	globalMessages, globalTotal, err := getGlobalMessageListPaginated(db, userId, msgType, isRead, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 2. 获取个人消息
	personalMessages, personalTotal, err := getPersonalMessageListPaginated(db, userId, msgType, isRead, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 3. 合并结果
	allMessages := MergeMessages(globalMessages, personalMessages, pageSize)
	total := globalTotal + personalTotal

	return allMessages, total, nil
}

// getGlobalMessageListPaginated 获取全员消息列表（分页）
func getGlobalMessageListPaginated(db *gorm.DB, userId uint, msgType constmap.MessageType, isRead int, page, pageSize int) ([]*MessageVO, int64, error) {
	// 构建查询条件
	query := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, COALESCE(message.is_read, 2) as is_read, message.read_time").
		Joins("LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?", userId).
		Where("message_tpl.type = ? AND message_tpl.scope = 1", msgType)

	// 添加已读状态筛选
	if isRead == constmap.Enable { // 已读
		query = query.Where("message.is_read = 1")
	} else if isRead == constmap.Disable { // 未读
		query = query.Where("(message.is_read IS NULL OR message.is_read = 2)")
	}

	// 统计总数
	var total int64
	countQuery := db.Model(&models.MessageTpl{}).
		Joins("LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?", userId).
		Where("message_tpl.type = ? AND message_tpl.scope = 1", msgType)

	if isRead == constmap.Enable {
		countQuery = countQuery.Where("message.is_read = 1")
	} else if isRead == constmap.Disable {
		countQuery = countQuery.Where("(message.is_read IS NULL OR message.is_read = 2)")
	}

	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, utils.NewError(err)
	}

	// 分页查询
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	err = query.Order("message_tpl.created_at DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&results).Error
	if err != nil {
		return nil, 0, utils.NewError(err)
	}

	// 转换为VO
	var messages []*MessageVO
	for _, result := range results {
		vo := &MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}

		// 转换ReadTime为unix时间戳
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}

		messages = append(messages, vo)
	}

	return messages, total, nil
}

// getPersonalMessageListPaginated 获取个人消息列表（分页）
func getPersonalMessageListPaginated(db *gorm.DB, userId uint, msgType constmap.MessageType, isRead int, page, pageSize int) ([]*MessageVO, int64, error) {
	// 构建查询条件
	query := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, message.is_read, message.read_time").
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId)

	// 添加已读状态筛选
	if isRead != 0 {
		query = query.Where("message.is_read = ?", isRead)
	}

	// 统计总数
	var total int64
	countQuery := db.Model(&models.MessageTpl{}).
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId)

	if isRead != 0 {
		countQuery = countQuery.Where("message.is_read = ?", isRead)
	}

	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, utils.NewError(err)
	}

	// 分页查询
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	err = query.Order("message_tpl.created_at DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&results).Error
	if err != nil {
		return nil, 0, utils.NewError(err)
	}

	// 转换为VO
	var messages []*MessageVO
	for _, result := range results {
		vo := &MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}

		// 转换ReadTime为unix时间戳
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}

		messages = append(messages, vo)
	}

	return messages, total, nil
}

// ClearUserMessageCache 清除用户消息相关缓存（优化版：批量删除）
func ClearUserMessageCache(userId uint) {
	// 收集所有需要删除的缓存key
	var keys []string

	// 用户消息状态缓存
	keys = append(keys, fmt.Sprintf(constmap.RKUserMsgStatus, userId))

	// 消息徽章缓存
	keys = append(keys, fmt.Sprintf(constmap.RKMsgBadge, userId))

	// 各类型消息相关缓存
	for _, msgType := range []constmap.MessageType{
		constmap.MessageTypeSystem,
		constmap.MessageTypeTeam,
		constmap.MessageTypeComment,
	} {
		// 消息汇总缓存
		keys = append(keys, fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType))
		// 消息统计缓存
		keys = append(keys, fmt.Sprintf(constmap.RKMsgTypeStats, userId, msgType))
	}

	// 使用pipeline批量删除
	my_cache.RedisClient().Del(keys...)
}

// 辅助函数：提取模板ID列表（使用slice.Map简化）
func extractTemplateIds(templates []*models.MessageTpl) []uint {
	return slice.Map(templates, func(_ int, tpl *models.MessageTpl) uint {
		return tpl.ID
	})
}
