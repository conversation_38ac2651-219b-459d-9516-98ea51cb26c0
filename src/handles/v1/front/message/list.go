package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 消息列表响应
type MessageListResponse struct {
	Total int64                    `json:"total"`
	List  []*message_biz.MessageVO `json:"list"`
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	// 使用 message_biz 的统一方法获取消息列表
	messages, total, err := message_biz.GetMessageListWithPagination(db, session.UserId, in.Type, in.IsRead, page, pageSize)
	if err != nil {
		return nil, err
	}

	return &MessageListResponse{
		Total: total,
		List:  messages,
	}, nil
}
